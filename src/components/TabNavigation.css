.tab-navigation {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
  overflow: hidden;
}

.tab-list {
  display: flex;
  background: #f8fafc;
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  font-size: 14px;
  color: #64748b;
  position: relative;
}

.tab-button:hover {
  background: #e2e8f0;
  color: #374151;
}

.tab-button.active {
  background: white;
  color: #1e293b;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.tab-icon {
  font-size: 16px;
}

.tab-label {
  font-weight: 600;
}

.tab-count {
  background: #e2e8f0;
  color: #64748b;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.tab-button.active .tab-count {
  background: #dbeafe;
  color: #2563eb;
}

.tab-button:hover .tab-count {
  background: #cbd5e1;
  color: #374151;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tab-navigation {
    background: #1e293b;
    border-color: #475569;
  }
  
  .tab-list {
    background: #334155;
  }
  
  .tab-button {
    color: #94a3b8;
  }
  
  .tab-button:hover {
    background: #475569;
    color: #e2e8f0;
  }
  
  .tab-button.active {
    background: #1e293b;
    color: #f1f5f9;
  }
  
  .tab-count {
    background: #475569;
    color: #94a3b8;
  }
  
  .tab-button.active .tab-count {
    background: #1e40af;
    color: #93c5fd;
  }
  
  .tab-button:hover .tab-count {
    background: #64748b;
    color: #e2e8f0;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .tab-button {
    padding: 12px 16px;
    font-size: 13px;
    gap: 6px;
  }
  
  .tab-icon {
    font-size: 14px;
  }
  
  .tab-count {
    font-size: 11px;
    padding: 1px 6px;
  }
}

@media (max-width: 480px) {
  .tab-button {
    flex-direction: column;
    gap: 4px;
    padding: 12px 8px;
  }
  
  .tab-label {
    font-size: 12px;
  }
}
