import React from 'react';
import './TabNavigation.css';

const TabNavigation = ({ activeTab, onTabChange, tabCounts }) => {
  const tabs = [
    { id: 'yesterday', label: 'Yesterday', icon: '📅' },
    { id: 'today', label: 'Today', icon: '🔴' },
    { id: 'tomorrow', label: 'Tomorrow', icon: '📆' }
  ];

  return (
    <div className="tab-navigation">
      <div className="tab-list">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => onTabChange(tab.id)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-label">{tab.label}</span>
            <span className="tab-count">{tabCounts[tab.id] || 0}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default TabNavigation;
