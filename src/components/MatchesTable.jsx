import React from 'react';
import { formatMatchDate, getCategoryIcon, getCategoryColor } from '../utils/dateUtils';
import './MatchesTable.css';

const MatchesTable = ({ matches, searchTerm, selectedCategory, showPopularOnly }) => {
  // Filter matches based on filters
  const filteredMatches = matches.filter(match => {
    const matchesCategory = selectedCategory === 'all' || match.category === selectedCategory;
    const matchesPopular = !showPopularOnly || match.popular;
    const matchesSearch = !searchTerm || 
      match.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesCategory && matchesPopular && matchesSearch;
  });

  if (filteredMatches.length === 0) {
    return (
      <div className="no-matches-table">
        <div className="no-matches-icon">🔍</div>
        <h3>No matches found</h3>
        <p>Try adjusting your filters or search terms.</p>
      </div>
    );
  }

  return (
    <div className="matches-table-container">
      <div className="table-wrapper">
        <table className="matches-table">
          <thead>
            <tr>
              <th className="col-status">Status</th>
              <th className="col-sport">Sport</th>
              <th className="col-match">Match</th>
              <th className="col-time">Time</th>
              <th className="col-sources">Sources</th>
              <th className="col-actions">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredMatches.map(match => (
              <tr key={match.id} className={`match-row ${match.popular ? 'popular' : ''}`}>
                <td className="col-status">
                  {match.popular && (
                    <div className="status-badge popular">
                      <span className="status-icon">🔥</span>
                      <span className="status-text">Popular</span>
                    </div>
                  )}
                </td>
                <td className="col-sport">
                  <div className="sport-info">
                    <span 
                      className="sport-icon"
                      style={{ color: getCategoryColor(match.category) }}
                    >
                      {getCategoryIcon(match.category)}
                    </span>
                    <span className="sport-name">{match.category.toUpperCase()}</span>
                  </div>
                </td>
                <td className="col-match">
                  <div className="match-title">{match.title}</div>
                </td>
                <td className="col-time">
                  <div className="match-time">{formatMatchDate(match.date)}</div>
                </td>
                <td className="col-sources">
                  <div className="sources-list">
                    {match.sources.map((source, index) => (
                      <span key={index} className="source-tag">
                        {source.source}
                      </span>
                    ))}
                  </div>
                </td>
                <td className="col-actions">
                  <button className="watch-button">
                    <span className="watch-icon">▶️</span>
                    Watch
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default MatchesTable;
