export const formatMatchDate = (timestamp) => {
  const date = new Date(timestamp);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const matchDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  const diffTime = matchDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  const timeString = date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });

  if (diffDays === 0) {
    return `Today at ${timeString}`;
  } else if (diffDays === 1) {
    return `Tomorrow at ${timeString}`;
  } else if (diffDays === -1) {
    return `Yesterday at ${timeString}`;
  } else if (diffDays > 1) {
    return `${date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })} at ${timeString}`;
  } else {
    return `${date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })} at ${timeString}`;
  }
};

export const getCategoryIcon = (category) => {
  const icons = {
    tennis: '🎾',
    football: '⚽',
    basketball: '🏀',
    baseball: '⚾',
    hockey: '🏒',
    soccer: '⚽',
    default: '🏆'
  };

  return icons[category.toLowerCase()] || icons.default;
};

export const getCategoryColor = (category) => {
  const colors = {
    tennis: '#4ade80',
    football: '#3b82f6',
    basketball: '#f97316',
    baseball: '#ef4444',
    hockey: '#8b5cf6',
    soccer: '#10b981',
    default: '#6b7280'
  };

  return colors[category.toLowerCase()] || colors.default;
};

export const categorizeMatchesByDate = (matches) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  const dayAfterTomorrow = new Date(today);
  dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

  const categorized = {
    yesterday: [],
    today: [],
    tomorrow: []
  };

  matches.forEach(match => {
    const matchDate = new Date(match.date);
    const matchDay = new Date(matchDate.getFullYear(), matchDate.getMonth(), matchDate.getDate());

    if (matchDay.getTime() === yesterday.getTime()) {
      categorized.yesterday.push(match);
    } else if (matchDay.getTime() === today.getTime()) {
      categorized.today.push(match);
    } else if (matchDay.getTime() === tomorrow.getTime()) {
      categorized.tomorrow.push(match);
    }
  });

  return categorized;
};
