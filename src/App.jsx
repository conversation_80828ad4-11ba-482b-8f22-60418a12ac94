import React, { useMemo, useState } from 'react';
import './App.css';
import ErrorMessage from './components/ErrorMessage';
import FilterBar from './components/FilterBar';
import Header from './components/Header';
import LoadingSpinner from './components/LoadingSpinner';
import useMatches from './hooks/useMatches';

function App() {
  const { matches, loading, error, refetch } = useMatches();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showPopularOnly, setShowPopularOnly] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Get unique categories from matches
  const categories = useMemo(() => {
    const uniqueCategories = [...new Set(matches.map(match => match.category))];
    return uniqueCategories.sort();
  }, [matches]);

  // Filter matches based on selected filters
  const filteredMatches = useMemo(() => {
    return matches.filter(match => {
      const matchesCategory = selectedCategory === 'all' || match.category === selectedCategory;
      const matchesPopular = !showPopularOnly || match.popular;
      const matchesSearch = !searchTerm ||
        match.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        match.category.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesCategory && matchesPopular && matchesSearch;
    });
  }, [matches, selectedCategory, showPopularOnly, searchTerm]);

  return (
    <div className="app">
      <Header
        matchCount={filteredMatches.length}
        onRefresh={refetch}
        isLoading={loading}
      />

      <main className="main-content">
        <div className="container">
          {!loading && !error && matches.length > 0 && (
            <FilterBar
              categories={categories}
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              showPopularOnly={showPopularOnly}
              onPopularToggle={setShowPopularOnly}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
            />
          )}

          {loading && <LoadingSpinner />}

          {error && (
            <ErrorMessage error={error} onRetry={refetch} />
          )}

          {!loading && !error && matches.length === 0 && (
            <div className="no-matches">
              <div className="no-matches-icon">📺</div>
              <h3>No matches available</h3>
              <p>There are no matches scheduled for today. Check back later!</p>
            </div>
          )}

          {!loading && !error && filteredMatches.length === 0 && matches.length > 0 && (
            <div className="no-matches">
              <div className="no-matches-icon">🔍</div>
              <h3>No matches found</h3>
              <p>Try adjusting your filters or search terms.</p>
            </div>
          )}

          {!loading && !error && filteredMatches.length > 0 && (
            <div className="matches-grid">
              {filteredMatches.map(match => (
                <MatchCard key={match.id} match={match} />
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
}

export default App;
