# FawaNews - Live Sports Matches App

A modern, professional React.js application that fetches and displays live sports matches with an attractive, responsive UI.

## Features

- **Real-time Match Data**: Fetches live sports matches from the Streamed API
- **Professional UI**: Modern, clean design with smooth animations and transitions
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Advanced Filtering**: Filter matches by sport category, popularity, and search terms
- **Dark Mode Support**: Automatic dark/light mode based on system preferences
- **Loading States**: Beautiful loading spinners and error handling
- **Match Categories**: Support for various sports including tennis, football, basketball, etc.
- **Popular Matches**: Highlighted popular matches with special badges

## Tech Stack

- **React 18** - Modern React with hooks
- **Vite** - Fast build tool and development server
- **CSS3** - Modern CSS with gradients, animations, and responsive design
- **Fetch API** - For API calls
- **Custom Hooks** - Reusable logic with useMatches hook

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Header.jsx      # App header with title and refresh button
│   ├── FilterBar.jsx   # Search and filter controls
│   ├── MatchCard.jsx   # Individual match display card
│   ├── LoadingSpinner.jsx # Loading animation
│   └── ErrorMessage.jsx   # Error display component
├── hooks/              # Custom React hooks
│   └── useMatches.js   # Hook for fetching matches data
├── utils/              # Utility functions
│   └── dateUtils.js    # Date formatting and category utilities
├── __tests__/          # Test files
│   └── useMatches.test.js # Tests for the matches hook
├── App.jsx             # Main application component
├── App.css             # Main application styles
├── index.css           # Global styles
└── main.jsx            # Application entry point
```

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd fawanews
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm test` - Run tests

## API Integration

The app fetches data from: `https://streamed.su/api/matches/all-today`

### API Response Format

```json
[
  {
    "id": "match-id",
    "title": "Match Title",
    "category": "tennis",
    "date": 1753560000000,
    "popular": true,
    "sources": [
      {
        "source": "alpha",
        "id": "match-source-id"
      }
    ]
  }
]
```

## Features in Detail

### Match Cards
- Display match title, category, and scheduled time
- Show popular matches with special badges
- List available streaming sources
- Responsive card layout with hover effects

### Filtering System
- **Search**: Search matches by title or category
- **Category Filter**: Filter by sport type (tennis, football, etc.)
- **Popular Toggle**: Show only popular matches
- **Real-time Filtering**: Instant results as you type

### Responsive Design
- Mobile-first approach
- Breakpoints for tablet and desktop
- Touch-friendly interface
- Optimized for all screen sizes

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
